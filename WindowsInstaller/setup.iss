; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!
; Ref: http://stackoverflow.com/questions/4833831/inno-setup-32bit-and-64bit-in-one

; ****  REQUIREMENTS FOR COMPILING THIS INSTALLER SCRIPT  ****
; The following files must exist in the directory defined by the SourceDir parameter under the [Setup] section below:
; msmpisetup.exe  (Microsoft MPI)
; VC_redist.x64.exe  (Microsoft C++ 2022 x64 redistributable)
; TauDEM 64-bit executables must exist under the following directory (this directory should exist under the dir defined by SourceDir parameter): 
; TauDEM_Exe/win_64/
; TauDEM ArcGIS toolbox related python files and the one .tbx file must exist under the following directory (this directory should exist under the dir defined by SourceDir parameter):
; TauDEMArcGIS/

; *** SOURCE CONTROL REQUIREMENTS ****
; All the files (as listed below) under the WindowsInstaller folder are included in source control
; This script file (setup.iss).
; taudem.bmp - Any time this file is updated it should be copied to the dir specified by the SourceDir param in the [Setup] section of this script


#define MyAppName "TauDEM"
#define MyAppVersion "5.3.8"
#define MyAppPublisher "Utah State University"
#define MyAppURL "http://hydrology.usu.edu/taudem/taudem5/index.html"
#define VcpkgDir "C:\dev\vcpkg"
#define SourcePath "..\TauDEM_Installation_Source"
#define GdalPluginsDir VcpkgDir + "\installed\x64-windows\lib\gdalplugins"
#define LibcurlPath VcpkgDir + "\installed\x64-windows\bin\libcurl.dll"
#define GdalVersion "3.10.3"
#define GdalInstallerVersion "1.0.2"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
; Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE. - this probably Visual Studio specific)
AppId={{101606B8-FCD5-4E2F-B976-6DC9D190A201}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
;AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={commonpf}\TauDEM
DefaultGroupName={#MyAppName}
OutputBaseFilename=TauDEM_setup_x64
Compression=lzma
SolidCompression=yes        
; "ArchitecturesInstallIn64BitMode=x64" requests that the install be
; done in "64-bit mode" on x64, meaning it should use the native
; 64-bit Program Files directory and the 64-bit view of the registry.
ArchitecturesInstallIn64BitMode=x64compatible
WizardSmallImageFile=taudem.bmp
; Don't show the welcome wizard page and ready to install page
DisableWelcomePage=yes
DisableReadyPage=yes
; The following source dir should have all the files and sub directories as outlined above (REQUIREMENTS FOR COMPILING THIS INSTALLER SCRIPT)
SourceDir={#SourcePath}

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Files]
; MS-MPI installer
Source: "msmpisetup.exe"; DestDir: "{app}\setup_files"; Flags: ignoreversion

; Visual C++ Redistributable (Visual Studio 2022)
Source: "VC_redist.x64.exe"; DestDir: "{app}\setup_files"; Flags: ignoreversion

; TauDEM executables (64-bit only)
Source: "TauDEM_Exe\win_64\*"; DestDir: "{app}\TauDEM5Exe"; Flags: ignoreversion recursesubdirs createallsubdirs

; TauDEM ArcGIS toolbox files
Source: "TauDEMArcGIS\*"; DestDir: "{app}\TauDEM5Arc"; Flags: ignoreversion recursesubdirs createallsubdirs

; GDAL from vcpkg (x64)
Source: "{#VcpkgDir}\installed\x64-windows\bin\gdal*.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\share\gdal\*"; DestDir: "{app}\share\gdal"; Flags: ignoreversion recursesubdirs

; PROJ library data files
Source: "{#VcpkgDir}\installed\x64-windows\share\proj\*"; DestDir: "{app}\share\proj"; Flags: ignoreversion recursesubdirs

; Essential GDAL dependencies
Source: "{#VcpkgDir}\installed\x64-windows\bin\proj_9.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\bin\sqlite3.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\bin\zlib1.dll"; DestDir: "{app}\bin"; Flags: ignoreversion

#if DirExists(GdalPluginsDir)
; GDAL plugins for SQLite
Source: "{#GdalPluginsDir}\*"; DestDir: "{app}\lib\gdalplugins"; Flags: ignoreversion recursesubdirs createallsubdirs
#endif

#if FileExists(LibcurlPath)
; Additional available GDAL dependencies
Source: "{#VcpkgDir}\installed\x64-windows\bin\libcurl.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
#endif

; Additional dependencies for shapefile support
Source: "{#VcpkgDir}\installed\x64-windows\bin\geotiff.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\bin\tiff.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\bin\jpeg62.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\bin\turbojpeg.dll"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\bin\json-c.dll"; DestDir: "{app}\bin"; Flags: ignoreversion

; GDAL utilities from tools feature
Source: "{#VcpkgDir}\installed\x64-windows\tools\gdal\*.exe"; DestDir: "{app}\bin"; Flags: ignoreversion
Source: "{#VcpkgDir}\installed\x64-windows\tools\gdal\*.dll"; DestDir: "{app}\bin"; Flags: ignoreversion

; Additional dependencies for OGR support with Spatialite
Source: "{#VcpkgDir}\installed\x64-windows\bin\spatialite.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist
Source: "{#VcpkgDir}\installed\x64-windows\bin\freexl-1.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist
Source: "{#VcpkgDir}\installed\x64-windows\bin\libexpat.dll"; DestDir: "{app}\bin"; Flags: ignoreversion

; Additional Spatialite-related dependencies
Source: "{#VcpkgDir}\installed\x64-windows\bin\geos.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist
Source: "{#VcpkgDir}\installed\x64-windows\bin\geos_c.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist
Source: "{#VcpkgDir}\installed\x64-windows\bin\liblzma.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist

; Ensure ALL GDAL plugins are copied (very important for OGR support)
Source: "{#VcpkgDir}\installed\x64-windows\lib\gdalplugins\*"; DestDir: "{app}\lib\gdalplugins"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist

; Copy all DLLs from vcpkg bin directory to ensure all dependencies are included - TODO: commenting out the following line 5-2-2025
; Source: "{#VcpkgDir}\installed\x64-windows\bin\*.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist

; Additional known OGR dependencies
Source: "{#VcpkgDir}\installed\x64-windows\bin\charset-1.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist
Source: "{#VcpkgDir}\installed\x64-windows\bin\libxml2.dll"; DestDir: "{app}\bin"; Flags: ignoreversion skipifsourcedoesntexist

; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Run]
; Install Visual C++ Redistributable
Filename: "{app}\setup_files\VC_redist.x64.exe"; Flags: waituntilterminated; Check: NeedsToInstallRedist2022()

; Install Microsoft MPI
Filename: "{app}\setup_files\msmpisetup.exe"; Flags: waituntilterminated shellexec; Check: NeedsToInstallMPI()

; First install GDAL installer package - needed for installing GDAL Python bindings for TauDEM integration with ArcGIS
Filename: "python"; Parameters: "-m pip install gdal-installer=={#GdalInstallerVersion}"; \
    Flags: waituntilterminated runhidden; \
    StatusMsg: "Installing GDAL installer package..."; \
    Check: HasPython() and WantsPythonGDAL()

; Run the GDAL installer Python script to install GDAL - this GDAL is needed for TauDEM integration with ArcGIS
Filename: "python"; \
    Parameters: "-m gdal_installer.install-gdal"; \
    Flags: waituntilterminated; \
    StatusMsg: "Running GDAL system installation..."; \
    Check: HasPython() and WantsPythonGDAL(); \
    AfterInstall: VerifyGdalInstallation

[Registry]
; Set TauDEM path and environment variables
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: string; ValueName: "PATH"; \
    ValueData: "{olddata};{app}\bin;{app}\TauDEM5Exe"; \
    Flags: preservestringtype

; Set GDAL_DATA for the vcpkg GDAL
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: expandsz; ValueName: "GDAL_DATA"; ValueData: "{app}\share\gdal"; Flags: uninsdeletevalue

; Set PROJ_LIB for GDAL to find proj.db
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: expandsz; ValueName: "PROJ_LIB"; ValueData: "{app}\share\proj"; Flags: uninsdeletevalue

; Set GDAL_DRIVER_PATH to point to bin directory AND plugins directory (critical for OGR)
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: expandsz; ValueName: "GDAL_DRIVER_PATH"; ValueData: "{app}\bin;{app}\lib\gdalplugins"; Flags: uninsdeletevalue

; Set OGR_DRIVER_PATH to point to both bin and plugins directories (critical)
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: expandsz; ValueName: "OGR_DRIVER_PATH"; ValueData: "{app}\bin;{app}\lib\gdalplugins"; Flags: uninsdeletevalue

; Add application directories to PATH
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: expandsz; ValueName: "Path"; ValueData: "{app}\bin;{app}\TauDEM5Exe;{olddata}"; Check: NeedsAddPath('{app}\bin')

; Enable all OGR drivers by default
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: string; ValueName: "GDAL_SKIP"; \
    ValueData: ""; \
    Flags: preservestringtype

; Set OGR_ENABLED_DRIVERS to ALL
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "OGR_ENABLED_DRIVERS"; \
    ValueData: ""; \
    Flags: uninsdeletevalue

; Remove any explicit driver count limit
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "OGR_DRIVER_COUNT"; \
    ValueData: ""; \
    Flags: uninsdeletevalue

; Set GDAL configuration for OGR with better error handling
; NOTE: Uncomment the following line to enable debugging when testing the installer
;Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
;    ValueType: expandsz; ValueName: "CPL_DEBUG"; \
;    ValueData: "ON"; \
;    Flags: uninsdeletevalue

; Configure error reporting for OGR
; NOTE: Uncomment the following line to enable error logging when testing the installer
; Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
;    ValueType: expandsz; ValueName: "CPL_LOG"; \
;    ValueData: "{app}\gdal_error.log"; \
;    Flags: uninsdeletevalue

; Enable VSI file system for GDAL
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "CPL_VSIL_USE_TEMP"; \
    ValueData: "YES"; \
    Flags: uninsdeletevalue

; Add configuration for SQLite and SpatiaLite
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "GDAL_SQLITE_PRAGMA"; \
    ValueData: "EMPTY_RESULT_CALLBACKS=ON"; \
    Flags: uninsdeletevalue

; Set SpatiaLite module path for GDAL
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "SPATIALITE_SECURITY"; \
    ValueData: "relaxed"; \
    Check: SpatiaLiteExists; Flags: uninsdeletevalue

; Add SpatiaLite data path only if it's going to exist
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "SPATIALITE_LIBRARY_PATH"; \
    ValueData: "{app}\bin\spatialite.dll"; \
    Check: SpatiaLiteExists; Flags: uninsdeletevalue

Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "CPL_LOG_ERRORS"; \
    ValueData: "ON"; \
    Flags: uninsdeletevalue

Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; \
    ValueType: expandsz; ValueName: "GDAL_ERROR_ON_LIBC_ERROR"; \
    ValueData: "TRUE"; \
    Flags: uninsdeletevalue

[code]
// add a custom wizard page after the welcome page to show the list of programs that will be installed
procedure InitializeWizard();
var UserPage: TInputQueryWizardPage;
var notes_string: string;
begin
  notes_string := 'NOTES:'#13'1. The redistributables listed above will only be installed if they are not already installed.'#13 +
      '2. You will need to accept the license agreements associated with this software and click through several screens.'#13 +
      '3. The installer will also add firewall exceptions to allow TauDEM programs to run. These allow MPI interprocess communication used in the parallel computations. This is communication within your computer and not over any external network.'#13 +
      '4. The installer will also configure the necessary environment variables for TauDEM, GDAL, and MPI.'; 
  UserPage := CreateInputQueryPage(wpWelcome,
    'The following components will be installed', '',
    'TauDEM version 5.3.8, GDAL (from vcpkg), Python GDAL bindings (if Python 3.10+ is available), ' +
    'Microsoft Visual C++ 2022 Redistributable Package (x64), Microsoft MPI'#13#13 + notes_string);   
end;

// Check if we need to install Visual C++ 2022 Redistributable
function NeedsToInstallRedist2022(): boolean;
begin
   // Check for Visual Studio 2022 redistributable (various possible registry locations)
   if RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\VisualStudio\14.30\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.30\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\VisualStudio\14.31\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.31\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\VisualStudio\14.32\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.32\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\VisualStudio\14.33\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.33\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\VisualStudio\14.34\VC\Runtimes\x64') or
      RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.34\VC\Runtimes\x64') then
   begin
      Result := False;
   end
   else
   begin
      Result := True;
   end;
end;

// Check if we need to install MPI
// If MPI is already installed, then this function returns False, otherwise True
function NeedsToInstallMPI(): boolean;
begin    
   if FileExists('C:\Windows\System32\msmpi.dll') then
      begin
        Result := False;
        exit;
      end
   else if RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\MPI') then
      begin
        Result := False;
        exit;
      end
   else
      begin
        Result := True;
      end;      
end;

// Simplified path checking function (removed architecture checking)
function NeedsAddPath(NewPath: string): boolean;
var
  OrigPath: string;
begin
  if not RegQueryStringValue(HKEY_LOCAL_MACHINE,
    'SYSTEM\CurrentControlSet\Control\Session Manager\Environment',
    'Path', OrigPath) then
  begin
    //MsgBox('Path variable not found.', mbInformation, MB_OK);
    //MsgBox('Path variable not found.', mbInformation, MB_OK);
    //MsgBox('Path variable not found.', mbInformation, MB_OK);
    Result := True;
    exit;
  end;
  // look for the path with leading and trailing semicolon
  // Pos() return
  //MsgBox('OrigPath:' + OrigPath, mbInformation, MB_OK);t0 if n
  //MsgBox('OrigPath:' + OrigPath, mbInformation, MB_OK);t found
  //MsgBox('OrigPath:' + OrigPath, mbInformation, MB_OK);
  Result := Pos(';' + UpperCase(ExpandConstant(NewPath)) + ';', ';' + UpperCase(OrigPath) + ';') = 0;
end;

procedure CleanUp(FolderToDelete: string);
begin
    if DirExists(ExpandConstant(FolderToDelete)) then
    begin
        DelTree(ExpandConstant(FolderToDelete), True, True, True);
        //MsgBox('Folder deleted', mbInformation, MB_OK);
    end;
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  // Check if running on 64-bit Windows
  if not IsWin64 then
  begin
    MsgBox('This installer requires 64-bit Windows.', mbError, MB_OK);
    Result := False;
  end;
end;

// Add a new function to check if SpatiaLite exists
function SpatiaLiteExists(): Boolean;
begin
  Result := FileExists(ExpandConstant('{app}\bin\spatialite.dll')) or 
           FileExists(ExpandConstant('{app}\bin\mod_spatialite.dll')) or
           FileExists(ExpandConstant('{app}\bin\sqlite3_mod_spatialite.dll'));
end;

// Check if Python is installed
function HasPython(): Boolean;
var
  ResultCode: Integer;
begin
  Result := False;
  
  // Try with python command
  if Exec('python', '-c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) then
  begin
    Result := (ResultCode = 0);
  end;
  
  // Try with py command as fallback
  if not Result then
  begin
    if Exec('py', '-c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) then
    begin
      Result := (ResultCode = 0);
    end;
  end;

  // Show message if Python version is too old
  if not Result then
  begin
    MsgBox('Python 3.10 or higher is required for GDAL installation.', mbInformation, MB_OK);
  end;
end;

// Ask user if they want to install GDAL Python bindings
function WantsPythonGDAL(): Boolean;
begin
  Result := MsgBox('Python is installed on your system. Would you like to install GDAL Python bindings? (Required for TauDEM integration with ArcGIS)', mbConfirmation, MB_YESNO) = IDYES;
end;

function CheckGdalPythonImport(): Boolean;
var
  ResultCode: Integer;
begin
  Result := False;
  
  // Try to import GDAL in Python and check version is 3.10+
  if Exec('python', '-c "from osgeo import gdal; import sys; version = tuple(map(int, gdal.__version__.split(''.''))); sys.exit(0 if version >= (3, 10) else 1)"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) then
  begin
    Result := (ResultCode = 0);
  end;
  
  if not Result then
  begin
    // Try with py command as fallback
    if Exec('py', '-c "from osgeo import gdal; import sys; version = tuple(map(int, gdal.__version__.split(''.''))); sys.exit(0 if version >= (3, 10) else 1)"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) then
    begin
      Result := (ResultCode = 0);
    end;
  end;
end;

procedure VerifyGdalInstallation;
begin
  if CheckGdalPythonImport() then
  begin
    MsgBox('GDAL Python bindings and system components were successfully installed and verified.', mbInformation, MB_OK);
  end
  else
  begin
    MsgBox('Warning: GDAL installation could not be verified.' + #13#10 + 
           'You may need to manually install GDAL after setup completes:' + #13#10 + 
           '1. Open a command prompt and run:' + #13#10 + 
           'python -m pip install gdal-installer==' + ExpandConstant('{#GdalInstallerVersion}') + #13#10 + 
           '2. Then run:' + #13#10 + 
           'python -m gdal_installer install-gdal', mbError, MB_OK);
  end;
end;
