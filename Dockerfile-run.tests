# This Dockerfile is for compiling TauDEM for Linux and running the tests. It is not intended for running TauDEM.

# Using Ubuntu as the base image
FROM ubuntu:22.04

# Install necessary dependencies to compile TauDEM
RUN apt update && apt install -y \
    build-essential \
    cmake \
    openmpi-bin \
    libopenmpi-dev \
    gdal-bin \
    libgdal-dev \
    libproj-dev \
    libtiff-dev \
    libgeotiff-dev \
    git \
    nano \
    sudo \
    && rm -rf /var/lib/apt/lists/*  # Clean up to reduce image size

# Install bats testing framework
RUN git clone https://github.com/sstephenson/bats.git && \
    cd bats && \
    ./install.sh /usr/local && \
    git clone https://github.com/ztombol/bats-support /tmp/bats-support && \
    git clone https://github.com/ztombol/bats-assert /tmp/bats-assert && \
    git clone https://github.com/ztombol/bats-file /tmp/bats-file

# Create a non-root user
RUN useradd -m -s /bin/bash taudem-docker

# Set the password for the taudem-docker user (less secure, for demonstration purposes)
RUN echo "taudem-docker:taudem-docker" | chpasswd

# Grant the taudem-docker user sudo privileges
RUN usermod -aG sudo taudem-docker

# Create workspace directory for taudem-docker
RUN mkdir -p /home/<USER>/workspace/TauDEM-Test-Data

# Clone the TauDEM-Test-Data repo and make the taudem-tests.sh executable for running the tests
# TODO: Change the branch back to main when ready
RUN chown taudem-docker:taudem-docker /home/<USER>/workspace/TauDEM-Test-Data && \
    sudo -u taudem-docker git clone -b 1-update-test-scripts --single-branch https://github.com/dtarb/TauDEM-Test-Data.git /home/<USER>/workspace/TauDEM-Test-Data && \
    chown taudem-docker:taudem-docker /home/<USER>/workspace/TauDEM-Test-Data/Input/taudem-tests.sh && \
    chmod +x /home/<USER>/workspace/TauDEM-Test-Data/Input/taudem-tests.sh

# Create the taudem directory
RUN mkdir -p /usr/local/taudem

# Set the PATH environment variable for taudem-docker user
# Setting the TAUDEM_PATH environment variable which is needed only for runinng the tests
# TAUDEM_PATH is used in the taudem-tests.sh script
RUN echo "export PATH=/usr/local/taudem:\$PATH" >> /home/<USER>/.bashrc && \
    echo "export TAUDEM_PATH=/usr/local/taudem" >> /home/<USER>/.bashrc

# Set the working directory inside the container - this is where we will run commands to build and install TauDEM
WORKDIR /app

# Set the default command to run an interactive shell
CMD ["/bin/bash"]