{"configurations": [{"name": "<PERSON>", "includePath": ["${workspaceFolder}/**", "/opt/homebrew/include", "/opt/homebrew/opt/open-mpi/include"], "defines": [], "compilerPath": "/opt/homebrew/opt/open-mpi/bin/mpicxx", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "macos-clang-arm64", "browse": {"path": ["${workspaceFolder}", "/opt/homebrew/include", "/opt/homebrew/opt/open-mpi/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}}, {"name": "Win32", "includePath": ["${workspaceFolder}/**", "C:/Program Files (x86)/Microsoft SDKs/MPI/Include", "C:/Program Files/GDAL/include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "compilerPath": "C:/mingw64/bin/g++.exe", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "browse": {"path": ["${workspaceFolder}", "C:/Program Files (x86)/Microsoft SDKs/MPI/Include", "C:/Program Files/GDAL/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}}], "version": 4}